/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ACTIVITY_TYPE, ChatActivityDto, ChatUserDto } from 'src/dto/dtos';
import { LogService } from 'src/log/log.service';
import { ChatUser } from 'src/models/chat-user.model';
import { Chat } from 'src/models/chat.model';
import { ChatActivity } from 'src/models/chat_activity_response.model';

@Injectable()
export class ActivityService {

  constructor(@InjectModel(ChatActivity.name) private readonly chatActivityModel: Model<ChatActivity>, private logService: LogService) { }
  async search(message: string, date: string, user: ChatUserDto) {
    const query = { tenantId: user.tenantId }

    if (message) {
      query['description'] = { $regex: message, $options: 'i' };
    }
    if (date) {
      query['createdAt'] = { $regex: date, $options: 'i' };
    }
    const activities = await this.chatActivityModel.find(query);
    return activities;
  }
  async updateReadStatus(id: string) {
    return await this.chatActivityModel.updateOne({ _id: id }, { isRead: true });
  }
  async createActivity(activity: ChatActivityDto) {
    const saved = await this.chatActivityModel.create(activity);
    await saved.save();
    return await this.findById(saved.id)
  }
  async findById(id: string) {
    return await this.chatActivityModel.findById(id).populate({
      path: 'message',
      model: 'ChatMessage'
    }).populate({
      path: 'sender',
      model: 'ChatUser',
      select: '-chats'
    }).populate({
      path: 'user',
      model: 'ChatUser',
      select: '-chats'
    }).populate({
      path: 'chat',
      model: 'Chat',
      select: '-users -jobProUserIds'
    });
  }
  async find(page: number, pageSize: number, activityType: ACTIVITY_TYPE, user: ChatUserDto, tenantId: string) {
    try {
      if (!page) {
        this.logService.log('page: ', page);
        page = 0;
      }
      if (!pageSize) pageSize = 100;
      console.log('page...: ', page, 'pageSize...: ', pageSize, ' activityType: ', activityType);
      console.log('User object: ', user);
      console.log('User ID: ', user.id);
      console.log('User jobProUserId: ', user.jobProUserId);

      const param = { user: user.id, tenantId };
      if (activityType) {
        param['activityType'] = activityType
      }
      console.log('Query Param: ', param);

      // Debug: Check what activities exist in the database
      const allActivities = await this.chatActivityModel.find({ tenantId }).limit(5);
      console.log('Sample activities in DB: ', allActivities);

      const paginated = await this.chatActivityModel.find(param).sort({ isRead: 1 }).sort({ createdAt: -1 }).limit(pageSize).skip(page * pageSize)
        .populate({
          path: 'message',
          model: 'ChatMessage'
        }).populate({
          path: 'sender',
          model: 'ChatUser',
          select: '-chats'
        }).populate({
          path: 'user',
          model: 'ChatUser',
          select: '-chats'
        }).populate({
          path: 'chat',
          model: 'Chat',
          select: '-users -jobProUserIds'
        });
      const unReadCount = await this.chatActivityModel.countDocuments({ isRead: false, user: user.id, tenantId })
      console.log('Query result count: ', paginated.length);
      console.log('UnRead count: ', unReadCount);
      console.log('Paginated result: ', paginated);
      return { paginatedActivity: paginated, page, pageSize, unReadCount };
    } catch (error) {
      this.logService.log('Error getting paginated chat activity data: ', error);
    }
  }

}
